load "raylib.ring"

class Engine {
    # خصائص المحرك
    
    func init
        oWindow = new WindowManager
        oRenderer = new Renderer
        oSceneManager = new SceneManager
        oResourceManager = new ResourceManager
        oInputManager = new InputManager
        oPhysicsEngine = new PhysicsEngine
        oAudioEngine = new AudioEngine

    func start
        while bIsRunning and !WindowShouldClose() {
            update()
            render()
        }
        cleanup()

    func update
        oInputManager.update()
        oPhysicsEngine.update()
        oSceneManager.update()
        oAudioEngine.update()

    func render
        oRenderer.beginFrame()
        oSceneManager.render()
        oRenderer.endFrame()

    func cleanup
        oResourceManager.cleanup()
        oAudioEngine.cleanup()
        CloseWindow()
        
    private
        oWindow
        oRenderer
        oSceneManager
        oResourceManager
        oInputManager
        oPhysicsEngine
        oAudioEngine
        bIsRunning = true
    
}
