class InputManager {
    

    func init
        initializeKeyStates()
        initializeMouseStates()
        initializeGamepadStates()

    func update
        updateKeyStates()
        updateMouseStates()
        updateGamepadStates()
        processInputEvents()

    func registerInputMapping cAction, nKey, nGamepadButton
        add(aInputMappings, new InputMapping(cAction, nKey, nGamepadButton))

    func addEventListener oListener
        add(aInputListeners, oListener)

    func removeEventListener oListener
        del(aInputListeners, find(aInputListeners, oListener))

    func isKeyPressed nKey
        return IsKeyPressed(nKey)

    func isKeyDown nKey
        return IsKeyDown(nKey)

    func isKeyReleased nKey
        return IsKeyReleased(nKey)

    func getMousePosition
        return GetMousePosition()

    func isMouseButtonPressed nButton
        return IsMouseButtonPressed(nButton)

    func getGamepadAxisMovement nGamepad, nAxis
        return GetGamepadAxisMovement(nGamepad, nAxis)

    private
        aKeyStates = []
        aMouseStates = []
        aGamepadStates = []
        aInputMappings = []
        aInputListeners = []

    func initializeKeyStates
        # تهيئة حالات المفاتيح

    func initializeMouseStates
        # تهيئة حالات الفأرة

    func initializeGamepadStates
        # تهيئة حالات يد التحكم

    func updateKeyStates
        # تحديث حالات المفاتيح

    func updateMouseStates
        # تحديث حالات الفأرة

    func updateGamepadStates
        # تحديث حالات يد التحكم

    func processInputEvents
        # معالجة أحداث الإدخال
        for oMapping in aInputMappings {
            if isActionTriggered(oMapping) {
                notifyListeners(oMapping.getAction())
            }
        }

    func isActionTriggered oMapping
        return isKeyPressed(oMapping.getKey()) or
               (oMapping.hasGamepadButton() and
                IsGamepadButtonPressed(0, oMapping.getGamepadButton()))

    func notifyListeners cAction
        for oListener in aInputListeners {
            oListener.onInputEvent(cAction)
        }
}
