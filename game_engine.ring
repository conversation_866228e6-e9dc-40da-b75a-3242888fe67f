load "raylib.ring"

# تهيئة المتغيرات الأساسية
nScreenWidth = 800
nScreenHeight = 450
cWindowTitle = "محرك الألعاب ثلاثي الأبعاد"

# تهيئة النافذة
InitWindow(nScreenWidth, nScreenHeight, cWindowTitle)
SetTargetFPS(60)

# تهيئة الكاميرا
oCamera = new Camera3D(
    0, 10, 10,    # position
    0, 0, 0,      # target
    0, 1, 0,      # up
    45,             # FOV
    CAMERA_PERSPECTIVE
)
cubePosition = Vector3(0, 0, 0)
# الحلقة الرئيسية للعبة
while ! WindowShouldClose() {
    # تحديث
    UpdateCamera(oCamera,CAMERA_FREE)
    
    # بداية الرسم
    BeginDrawing()
        ClearBackground(RAYWHITE)
        BeginMode3D(oCamera)
            # رسم الأرضية
            DrawGrid(10, 1.0)
            # رسم مكعب للتجربة
            DrawCube(cubePosition, 2, 2, 2, RED)
        EndMode3D()
        
        # رسم معلومات FPS
        DrawFPS(10, 10)
    EndDrawing()
}

# إغلاق النافذة
CloseWindow()
