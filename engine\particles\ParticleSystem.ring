class ParticleSystem {
   

    func init
        # تحميل الشيدر الخاص بالجسيمات
        oShader = LoadShader("shaders/particle.vert", "shaders/particle.frag")
        initializeBuffers()

    func createEmitter
        oEmitter = new ParticleEmitter {
            aPosition = [0, 0, 0]
            aDirection = [0, 1, 0]
            nSpread = 45
            nParticlesPerSecond = 100
            nLifetime = 2.0
            nStartSize = 1.0
            nEndSize = 0.1
            aStartColor = [1, 1, 1, 1]
            aEndColor = [1, 1, 1, 0]
            nStartSpeed = 5.0
            nEndSpeed = 1.0
            bGravityAffected = true
        }
        add(aEmitters, oEmitter)
        return oEmitter

    func update
        nDeltaTime = GetFrameTime()
        
        # تحديث كل مولد جسيمات
        for oEmitter in aEmitters {
            # إنشاء جسيمات جديدة
            nNewParticles = oEmitter.getParticlesPerFrame(nDeltaTime)
            for i = 1 to nNewParticles {
                if len(aParticles) < nMaxParticles {
                    add(aParticles, oEmitter.emitParticle())
                }
            }
        }

        # تحديث الجسيمات الموجودة
        updateParticles(nDeltaTime)
        removeDeadParticles()

    func updateParticles nDeltaTime
        for oParticle in aParticles {
            # تحديث الموقع
            oParticle.aPosition[1] = oParticle.aPosition[1] + 
                                   oParticle.aVelocity[1] * nDeltaTime

            # تحديث السرعة (مع الجاذبية)
            if oParticle.bGravityAffected {
                oParticle.aVelocity[2] = oParticle.aVelocity[2] - 9.81 * nDeltaTime
            }

            # تحديث الحجم
            oParticle.nSize = lerp(oParticle.nStartSize, 
                                 oParticle.nEndSize, 
                                 oParticle.getLifePercent())

            # تحديث اللون
            oParticle.aColor = lerpColor(oParticle.aStartColor,
                                       oParticle.aEndColor,
                                       oParticle.getLifePercent())

            # تحديث العمر
            oParticle.nLifeRemaining -= nDeltaTime
        }

    func removeDeadParticles
        aNewParticles = []
        for oParticle in aParticles {
            if oParticle.nLifeRemaining > 0 {
                add(aNewParticles, oParticle)
            }
        }
        aParticles = aNewParticles

    func render oCamera
        BeginShaderMode(oShader)
        # رسم كل الجسيمات
        for oParticle in aParticles {
            DrawBillboard(oCamera, 
                         oTexture,
                         oParticle.aPosition,
                         oParticle.nSize,
                         oParticle.aColor)
        }
        EndShaderMode()

     private
        aParticles = []
        aEmitters = []
        oShader
        oTexture
        nMaxParticles = 10000

    func initializeBuffers
        # تهيئة الذاكرة المؤقتة للجسيمات

    func lerp x, y, a
        return x * (1-a) + y * a

    func lerpColor c1, c2, a
        return [
            lerp(c1[1], c2[1], a),
            lerp(c1[2], c2[2], a),
            lerp(c1[3], c2[3], a),
            lerp(c1[4], c2[4], a)
        ]

    func cleanup
        UnloadShader(oShader)
        if oTexture {
            UnloadTexture(oTexture)
        }
}
