load "raylib.ring"

class SceneEditor {
    

    func init
        oGizmo = new TransformGizmo
        initializeCamera()
        initializeUI()

    func initializeCamera
        oCamera = new Camera3D(
            [10, 10, 10],   # موقع
            [0, 0, 0],      # هدف
            [0, 1, 0],      # اتجاه
            45,             # زاوية الرؤية
            CAMERA_PERSPECTIVE
        )

    func initializeUI
        oUI = new EditorUI {
            # تهيئة عناصر واجهة المستخدم
            oHierarchyPanel = new HierarchyPanel
            oInspectorPanel = new InspectorPanel
            oToolbar = new Toolbar
            oMenuBar = new MenuBar
        }

    func update
        handleInput()
        updateCamera()
        updateGizmo()
        updateUI()

    func render
        BeginDrawing()
            ClearBackground(RAYWHITE)
            BeginMode3D(oCamera)
                if bGridEnabled {
                    DrawGrid(20, nGridSize)
                }
                renderScene()
                if oSelectedObject {
                    oGizmo.render(oSelectedObject)
                }
            EndMode3D()
            
            # رسم واجهة المستخدم
            oUI.render()
        EndDrawing()

    func handleInput
        # التعامل مع مدخلات المحرر
        if IsMouseButtonPressed(MOUSE_LEFT_BUTTON) {
            handleSelection()
        }

        if IsKeyPressed(KEY_DELETE) and oSelectedObject {
            deleteSelectedObject()
        }

        # اختصارات لوحة المفاتيح
        if IsKeyDown(KEY_LEFT_CONTROL) {
            if IsKeyPressed(KEY_Z) {
                undo()
            }
            if IsKeyPressed(KEY_Y) {
                redo()
            }
            if IsKeyPressed(KEY_S) {
                saveScene()
            }
        }

    func handleSelection
        if not IsMouseButtonDown(MOUSE_LEFT_BUTTON) {
            return
        }

        oRay = GetMouseRay(GetMousePosition(), oCamera)
        oSelectedObject = performRaycast(oRay)
        
        if oSelectedObject {
            oUI.updateInspector(oSelectedObject)
        }

    func addGameObject cType
        oNewObject = new GameObject(cType)
        if bSnapToGrid {
            snapToGrid(oNewObject)
        }
        
        if oCurrentScene {
            oCurrentScene.addObject(oNewObject)
            addToUndoStack("Add " + cType)
        }

        return oNewObject

    func deleteSelectedObject
        if oSelectedObject {
            addToUndoStack("Delete " + oSelectedObject.getName())
            oCurrentScene.removeObject(oSelectedObject)
            oSelectedObject = null
        }

    func saveScene
        if oCurrentScene {
            oCurrentScene.save()
        }

    func loadScene cPath
        oCurrentScene = new Scene
        oCurrentScene.load(cPath)

    func undo
        if len(aUndoStack) > 0 {
            oAction = aUndoStack[len(aUndoStack)]
            del(aUndoStack, len(aUndoStack))
            add(aRedoStack, oAction)
            oAction.undo()
        }

    func redo
        if len(aRedoStack) > 0 {
            oAction = aRedoStack[len(aRedoStack)]
            del(aRedoStack, len(aRedoStack))
            add(aUndoStack, oAction)
            oAction.redo()
        }

    private
        oCurrentScene
        oSelectedObject
        oGizmo
        oCamera
        oUI
        bGridEnabled = true
        nGridSize = 1.0
        bSnapToGrid = true
        aUndoStack = []
        aRedoStack = []

    func addToUndoStack cDescription
        oAction = new EditorAction(cDescription)
        add(aUndoStack, oAction)
        aRedoStack = [] # مسح ستاك الإعادة عند إضافة إجراء جديد

    func snapToGrid oObject
        aPos = oObject.getPosition()
        for i = 1 to 3 {
            aPos[i] = round(aPos[i] / nGridSize) * nGridSize
        }
        oObject.setPosition(aPos)

    func performRaycast oRay
        # تنفيذ عملية الريكاست وإرجاع الكائن المحدد
}
