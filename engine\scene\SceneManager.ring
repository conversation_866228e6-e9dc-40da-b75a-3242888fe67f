class SceneManager {
    

    func init
        oSceneGraph = new SceneGraph

    func loadScene cScenePath
        oNewScene = new Scene
        oNewScene.load(cScenePath)
        add(aScenes, oNewScene)
        if len(aScenes) = 1 {
            setCurrentScene(oNewScene)
        }

    func setCurrentScene oScene
        if oCurrentScene {
            oCurrentScene.onExit()
        }
        oCurrentScene = oScene
        oCurrentScene.onEnter()

    func update
        if oCurrentScene {
            oCurrentScene.update()
            oSceneGraph.update()
        }

    func render
        if oCurrentScene {
            oCurrentScene.render()
        }

    func addGameObject oObject, cParentID = ""
        oSceneGraph.addNode(oObject, cParentID)

    func removeGameObject oObject
        oSceneGraph.removeNode(oObject)

    func findGameObject cID
        return oSceneGraph.findNode(cID)

    func cleanup
        for oScene in aScenes {
            oScene.cleanup()
        }
        oSceneGraph.cleanup()
        
    private
        aScenes = []
        oCurrentScene
        oSceneGraph
}
